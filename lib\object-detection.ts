import { DetectedObject } from '@/types/image';

/**
 * Detect objects using a simple center-based approach
 * This simulates object detection for the MVP
 */
export async function detectObjects(imageData: string): Promise<DetectedObject[]> {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 1000));

  try {
    // Create image element to analyze
    const img = new Image();
    img.crossOrigin = 'anonymous';

    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageData;
    });

    // Use simple heuristics to detect likely object position
    // In a real app, this would use actual AI detection
    const detectedObject: DetectedObject = {
      x: 25, // 25% from left
      y: 15, // 15% from top
      width: 50, // 50% of image width
      height: 70, // 70% of image height
      confidence: 0.90,
      type: 'person',
    };

    return [detectedObject];
  } catch (error) {
    console.error('Object detection failed:', error);
    // Return default detection
    return [{
      x: 25,
      y: 15,
      width: 50,
      height: 70,
      confidence: 0.85,
      type: 'person',
    }];
  }
}

/**
 * Analyze the foreground image to find the bounding box of the object
 */
async function analyzeObjectBounds(img: HTMLImageElement): Promise<{ x: number; y: number; width: number; height: number } | null> {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  canvas.width = img.width;
  canvas.height = img.height;
  ctx.drawImage(img, 0, 0);

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  let minX = canvas.width, minY = canvas.height, maxX = 0, maxY = 0;
  let hasPixels = false;

  // Find bounding box of non-transparent pixels
  for (let y = 0; y < canvas.height; y++) {
    for (let x = 0; x < canvas.width; x++) {
      const alpha = data[(y * canvas.width + x) * 4 + 3];
      if (alpha > 0) { // Non-transparent pixel
        hasPixels = true;
        minX = Math.min(minX, x);
        minY = Math.min(minY, y);
        maxX = Math.max(maxX, x);
        maxY = Math.max(maxY, y);
      }
    }
  }

  if (!hasPixels) return null;

  // Convert to percentages
  return {
    x: (minX / canvas.width) * 100,
    y: (minY / canvas.height) * 100,
    width: ((maxX - minX) / canvas.width) * 100,
    height: ((maxY - minY) / canvas.height) * 100,
  };
}

/**
 * Calculate the optimal position for text behind the detected object
 */
export function calculateTextPosition(
  detectedObject: DetectedObject,
  imageWidth: number,
  imageHeight: number
): { x: number; y: number } {
  // Place text slightly behind and to the side of the detected object
  const textX = Math.max(5, detectedObject.x - 10); // 10% to the left, minimum 5%
  const textY = detectedObject.y + (detectedObject.height * 0.3); // 30% down from object top

  return {
    x: Math.min(textX, 80), // Maximum 80% to ensure text fits
    y: Math.min(textY, 80), // Maximum 80% to ensure text fits
  };
}

/**
 * Check if any objects were detected in the image
 */
export function hasDetectedObjects(objects: DetectedObject[]): boolean {
  return objects.length > 0 && objects.some(obj => obj.confidence > 0.5);
}
