import { DetectedObject } from '@/types/image';

/**
 * Placeholder object detection function
 * In a real implementation, this would use TensorFlow.js or similar AI library
 * For now, it returns a mock detected object in the center of the image
 */
export async function detectObjects(imageData: string): Promise<DetectedObject[]> {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Create a mock detected object (person) in the center of the image
  // In a real implementation, this would analyze the actual image
  const mockDetectedObject: DetectedObject = {
    x: 30, // 30% from left
    y: 20, // 20% from top
    width: 40, // 40% of image width
    height: 60, // 60% of image height
    confidence: 0.85,
    type: 'person',
  };

  return [mockDetectedObject];
}

/**
 * Calculate the optimal position for text behind the detected object
 */
export function calculateTextPosition(
  detectedObject: DetectedObject,
  imageWidth: number,
  imageHeight: number
): { x: number; y: number } {
  // Place text slightly behind and to the side of the detected object
  const textX = Math.max(5, detectedObject.x - 10); // 10% to the left, minimum 5%
  const textY = detectedObject.y + (detectedObject.height * 0.3); // 30% down from object top

  return {
    x: Math.min(textX, 80), // Maximum 80% to ensure text fits
    y: Math.min(textY, 80), // Maximum 80% to ensure text fits
  };
}

/**
 * Check if any objects were detected in the image
 */
export function hasDetectedObjects(objects: DetectedObject[]): boolean {
  return objects.length > 0 && objects.some(obj => obj.confidence > 0.5);
}
