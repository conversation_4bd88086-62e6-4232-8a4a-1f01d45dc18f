import { TextOverlay, DetectedObject } from '@/types/image';

/**
 * Create a canvas with text that appears behind the object using visual techniques
 */
export async function createImageWithTextBehind(
  imageElement: HTMLImageElement,
  textOverlay: TextOverlay,
  detectedObject?: DetectedObject
): Promise<HTMLCanvasElement> {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  // Set canvas size to match image
  canvas.width = imageElement.naturalWidth;
  canvas.height = imageElement.naturalHeight;

  try {
    // Step 1: Draw the original image
    ctx.drawImage(imageElement, 0, 0);

    // Step 2: Create a "behind" effect using layering and masking
    if (detectedObject) {
      await createTextBehindEffect(ctx, canvas, textOverlay, detectedObject, imageElement);
    } else {
      // Fallback to simple text overlay
      drawSimpleText(ctx, canvas, textOverlay);
    }

    return canvas;
  } catch (error) {
    console.error('Failed to create image with text behind:', error);
    // Fallback to simple overlay
    return createSimpleTextOverlay(imageElement, textOverlay);
  }
}

/**
 * Create the "behind" effect using advanced canvas techniques
 */
async function createTextBehindEffect(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  textOverlay: TextOverlay,
  detectedObject: DetectedObject,
  originalImage: HTMLImageElement
): Promise<void> {
  // Calculate object bounds in pixels
  const objX = (detectedObject.x / 100) * canvas.width;
  const objY = (detectedObject.y / 100) * canvas.height;
  const objWidth = (detectedObject.width / 100) * canvas.width;
  const objHeight = (detectedObject.height / 100) * canvas.height;

  // Calculate text position (behind the object)
  const textX = (textOverlay.x / 100) * canvas.width;
  const textY = (textOverlay.y / 100) * canvas.height;

  // Create a temporary canvas for the text
  const textCanvas = document.createElement('canvas');
  const textCtx = textCanvas.getContext('2d');
  if (!textCtx) return;

  textCanvas.width = canvas.width;
  textCanvas.height = canvas.height;

  // Draw text on temporary canvas
  textCtx.font = `${textOverlay.fontSize}px ${textOverlay.fontFamily}`;
  textCtx.fillStyle = textOverlay.color;
  textCtx.textAlign = 'center';
  textCtx.textBaseline = 'middle';

  if (textOverlay.rotation !== 0) {
    textCtx.save();
    textCtx.translate(textX, textY);
    textCtx.rotate((textOverlay.rotation * Math.PI) / 180);
    textCtx.fillText(textOverlay.content, 0, 0);
    textCtx.restore();
  } else {
    textCtx.fillText(textOverlay.content, textX, textY);
  }

  // Create mask for the object area
  const maskCanvas = document.createElement('canvas');
  const maskCtx = maskCanvas.getContext('2d');
  if (!maskCtx) return;

  maskCanvas.width = canvas.width;
  maskCanvas.height = canvas.height;

  // Draw a white rectangle for the object area (this will be the "foreground")
  maskCtx.fillStyle = 'white';
  maskCtx.fillRect(objX, objY, objWidth, objHeight);

  // Apply the mask to hide text in the object area
  textCtx.globalCompositeOperation = 'destination-out';
  textCtx.drawImage(maskCanvas, 0, 0);

  // Draw the text (now with object area cut out) onto main canvas
  ctx.drawImage(textCanvas, 0, 0);

  // Redraw the original image with some transparency in non-object areas
  // to make the text more visible
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  // Slightly darken areas where text appears to enhance the "behind" effect
  for (let y = 0; y < canvas.height; y++) {
    for (let x = 0; x < canvas.width; x++) {
      const index = (y * canvas.width + x) * 4;

      // Check if this pixel is outside the object area
      if (x < objX || x > objX + objWidth || y < objY || y > objY + objHeight) {
        // Check if there's text here by sampling the text canvas
        const textImageData = textCtx.getImageData(x, y, 1, 1);
        if (textImageData.data[3] > 0) { // If there's text alpha
          // Slightly darken the background to make text more visible
          data[index] = Math.max(0, data[index] - 20);     // R
          data[index + 1] = Math.max(0, data[index + 1] - 20); // G
          data[index + 2] = Math.max(0, data[index + 2] - 20); // B
        }
      }
    }
  }

  ctx.putImageData(imageData, 0, 0);
}

/**
 * Draw simple text overlay
 */
function drawSimpleText(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  textOverlay: TextOverlay
): void {
  const textX = (textOverlay.x / 100) * canvas.width;
  const textY = (textOverlay.y / 100) * canvas.height;

  ctx.font = `${textOverlay.fontSize}px ${textOverlay.fontFamily}`;
  ctx.fillStyle = textOverlay.color;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // Add text shadow for better visibility
  ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
  ctx.shadowBlur = 4;
  ctx.shadowOffsetX = 2;
  ctx.shadowOffsetY = 2;

  if (textOverlay.rotation !== 0) {
    ctx.save();
    ctx.translate(textX, textY);
    ctx.rotate((textOverlay.rotation * Math.PI) / 180);
    ctx.fillText(textOverlay.content, 0, 0);
    ctx.restore();
  } else {
    ctx.fillText(textOverlay.content, textX, textY);
  }

  // Reset shadow
  ctx.shadowColor = 'transparent';
  ctx.shadowBlur = 0;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 0;
}

/**
 * Fallback function for simple text overlay (original implementation)
 */
function createSimpleTextOverlay(
  imageElement: HTMLImageElement,
  textOverlay: TextOverlay
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  canvas.width = imageElement.naturalWidth;
  canvas.height = imageElement.naturalHeight;

  ctx.drawImage(imageElement, 0, 0);

  const textX = (textOverlay.x / 100) * canvas.width;
  const textY = (textOverlay.y / 100) * canvas.height;

  ctx.font = `${textOverlay.fontSize}px ${textOverlay.fontFamily}`;
  ctx.fillStyle = textOverlay.color;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  if (textOverlay.rotation !== 0) {
    ctx.save();
    ctx.translate(textX, textY);
    ctx.rotate((textOverlay.rotation * Math.PI) / 180);
    ctx.fillText(textOverlay.content, 0, 0);
    ctx.restore();
  } else {
    ctx.fillText(textOverlay.content, textX, textY);
  }

  return canvas;
}

/**
 * Convert blob to image element
 */
function blobToImage(blob: Blob): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(blob);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve(img);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image from blob'));
    };

    img.src = url;
  });
}

/**
 * Convert canvas to blob for download
 */
export function canvasToBlob(canvas: HTMLCanvasElement, format: 'png' | 'jpeg' = 'png'): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to convert canvas to blob'));
        }
      },
      format === 'jpeg' ? 'image/jpeg' : 'image/png',
      0.9 // Quality for JPEG
    );
  });
}

/**
 * Download the processed image
 */
export function downloadImage(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Load image from file or URL
 */
export function loadImage(src: string | File): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    
    if (src instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          img.src = e.target.result as string;
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(src);
    } else {
      img.src = src;
    }
  });
}
