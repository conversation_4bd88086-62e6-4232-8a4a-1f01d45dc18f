import { TextOverlay, DetectedObject } from '@/types/image';

// Dynamic import to handle potential issues with the background removal library
let removeBackgroundModule: any = null;

async function getRemoveBackground() {
  if (!removeBackgroundModule) {
    try {
      removeBackgroundModule = await import('@imgly/background-removal');
      return removeBackgroundModule.removeBackground;
    } catch (error) {
      console.error('Failed to load background removal module:', error);
      return null;
    }
  }
  return removeBackgroundModule.removeBackground;
}

/**
 * Create a canvas with text placed behind the object
 */
export async function createImageWithTextBehind(
  imageElement: HTMLImageElement,
  textOverlay: TextOverlay,
  detectedObject?: DetectedObject
): Promise<HTMLCanvasElement> {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  // Set canvas size to match image
  canvas.width = imageElement.naturalWidth;
  canvas.height = imageElement.naturalHeight;

  try {
    const removeBackground = await getRemoveBackground();

    if (!removeBackground) {
      console.log('Background removal not available, using simple overlay');
      return createSimpleTextOverlay(imageElement, textOverlay);
    }

    // Configure background removal
    const config = {
      model: 'isnet',
      output: {
        format: 'image/png',
        quality: 0.8,
      },
      debug: false,
    };

    // Step 1: Remove background to get foreground object
    const foregroundBlob = await removeBackground(imageElement, config);

    if (!foregroundBlob) {
      console.log('Background removal returned null, using simple overlay');
      return createSimpleTextOverlay(imageElement, textOverlay);
    }

    const foregroundImg = await blobToImage(foregroundBlob);

    // Step 2: Draw original image as background
    ctx.drawImage(imageElement, 0, 0);

    // Step 3: Draw text on top of background
    const textX = (textOverlay.x / 100) * canvas.width;
    const textY = (textOverlay.y / 100) * canvas.height;

    ctx.font = `${textOverlay.fontSize}px ${textOverlay.fontFamily}`;
    ctx.fillStyle = textOverlay.color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    if (textOverlay.rotation !== 0) {
      ctx.save();
      ctx.translate(textX, textY);
      ctx.rotate((textOverlay.rotation * Math.PI) / 180);
      ctx.fillText(textOverlay.content, 0, 0);
      ctx.restore();
    } else {
      ctx.fillText(textOverlay.content, textX, textY);
    }

    // Step 4: Draw foreground object on top (this makes text appear behind)
    ctx.drawImage(foregroundImg, 0, 0, canvas.width, canvas.height);

    return canvas;
  } catch (error) {
    console.error('Failed to create image with text behind:', error);
    // Fallback to simple overlay if background removal fails
    return createSimpleTextOverlay(imageElement, textOverlay);
  }
}

/**
 * Fallback function for simple text overlay (original implementation)
 */
function createSimpleTextOverlay(
  imageElement: HTMLImageElement,
  textOverlay: TextOverlay
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  canvas.width = imageElement.naturalWidth;
  canvas.height = imageElement.naturalHeight;

  ctx.drawImage(imageElement, 0, 0);

  const textX = (textOverlay.x / 100) * canvas.width;
  const textY = (textOverlay.y / 100) * canvas.height;

  ctx.font = `${textOverlay.fontSize}px ${textOverlay.fontFamily}`;
  ctx.fillStyle = textOverlay.color;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  if (textOverlay.rotation !== 0) {
    ctx.save();
    ctx.translate(textX, textY);
    ctx.rotate((textOverlay.rotation * Math.PI) / 180);
    ctx.fillText(textOverlay.content, 0, 0);
    ctx.restore();
  } else {
    ctx.fillText(textOverlay.content, textX, textY);
  }

  return canvas;
}

/**
 * Convert blob to image element
 */
function blobToImage(blob: Blob): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(blob);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve(img);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image from blob'));
    };

    img.src = url;
  });
}

/**
 * Convert canvas to blob for download
 */
export function canvasToBlob(canvas: HTMLCanvasElement, format: 'png' | 'jpeg' = 'png'): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to convert canvas to blob'));
        }
      },
      format === 'jpeg' ? 'image/jpeg' : 'image/png',
      0.9 // Quality for JPEG
    );
  });
}

/**
 * Download the processed image
 */
export function downloadImage(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Load image from file or URL
 */
export function loadImage(src: string | File): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    
    if (src instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          img.src = e.target.result as string;
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(src);
    } else {
      img.src = src;
    }
  });
}
