import { TextOverlay, DetectedObject } from '@/types/image';

/**
 * Create a canvas with the image and text overlay
 */
export function createImageWithText(
  imageElement: HTMLImageElement,
  textOverlay: TextOverlay,
  detectedObject?: DetectedObject
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  // Set canvas size to match image
  canvas.width = imageElement.naturalWidth;
  canvas.height = imageElement.naturalHeight;

  // Draw the original image
  ctx.drawImage(imageElement, 0, 0);

  // Calculate text position in pixels
  const textX = (textOverlay.x / 100) * canvas.width;
  const textY = (textOverlay.y / 100) * canvas.height;

  // Set text properties
  ctx.font = `${textOverlay.fontSize}px ${textOverlay.fontFamily}`;
  ctx.fillStyle = textOverlay.color;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // Apply rotation if needed
  if (textOverlay.rotation !== 0) {
    ctx.save();
    ctx.translate(textX, textY);
    ctx.rotate((textOverlay.rotation * Math.PI) / 180);
    ctx.fillText(textOverlay.content, 0, 0);
    ctx.restore();
  } else {
    ctx.fillText(textOverlay.content, textX, textY);
  }

  return canvas;
}

/**
 * Convert canvas to blob for download
 */
export function canvasToBlob(canvas: HTMLCanvasElement, format: 'png' | 'jpeg' = 'png'): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to convert canvas to blob'));
        }
      },
      format === 'jpeg' ? 'image/jpeg' : 'image/png',
      0.9 // Quality for JPEG
    );
  });
}

/**
 * Download the processed image
 */
export function downloadImage(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Load image from file or URL
 */
export function loadImage(src: string | File): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    
    if (src instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          img.src = e.target.result as string;
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(src);
    } else {
      img.src = src;
    }
  });
}
