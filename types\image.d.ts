export interface ImageUpload {
  file: File;
  preview: string;
  width: number;
  height: number;
}

export interface ProcessedImage {
  originalImage: string;
  processedImage: string;
  detectedObjects: DetectedObject[];
}

export interface DetectedObject {
  x: number;
  y: number;
  width: number;
  height: number;
  confidence: number;
  type: 'person' | 'object';
}

export interface TextOverlay {
  content: string;
  x: number;
  y: number;
  fontSize: number;
  fontFamily: string;
  color: string;
  rotation: number;
}

export interface ImageProcessingResult {
  success: boolean;
  processedImageUrl?: string;
  error?: string;
  detectedObjects?: DetectedObject[];
}
