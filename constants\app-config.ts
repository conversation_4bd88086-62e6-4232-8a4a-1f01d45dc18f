export const APP_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png'],
  SUPPORTED_EXTENSIONS: ['.jpg', '.jpeg', '.png'],
} as const;

export const DEFAULT_TEXT_CONFIG = {
  content: 'POV',
  fontSize: 60,
  fontFamily: 'Arial',
  color: '#FFFFFF',
  x: 50, // percentage
  y: 50, // percentage
  rotation: 0,
} as const;

export const FONT_OPTIONS = [
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Georgia',
  'Verdana',
  'Courier New',
  'Impact',
  'Comic Sans MS',
] as const;

export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File too large. Maximum size is 5MB.',
  INVALID_FORMAT: 'Unsupported file type. Please upload a JPG or PNG image.',
  NO_OBJECT_DETECTED: 'No object detected. Please try another image.',
  PROCESSING_ERROR: 'An error occurred during processing. Please try again later.',
  UPLOAD_FAILED: 'Failed to upload image. Please try again.',
} as const;
