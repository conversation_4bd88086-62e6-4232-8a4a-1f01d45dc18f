import { APP_CONFIG, ERROR_MESSAGES } from '@/constants/app-config';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export function validateImageFile(file: File): ValidationResult {
  // Check file size
  if (file.size > APP_CONFIG.MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.FILE_TOO_LARGE,
    };
  }

  // Check file type
  if (!APP_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.INVALID_FORMAT,
    };
  }

  // Check file extension as additional validation
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  if (!APP_CONFIG.SUPPORTED_EXTENSIONS.includes(extension)) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.INVALID_FORMAT,
    };
  }

  return { isValid: true };
}

export function createImagePreview(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
}
