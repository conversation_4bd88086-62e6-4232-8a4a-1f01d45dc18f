'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Download, Type, Palette, Move } from 'lucide-react';
import { ImageUpload, TextOverlay, DetectedObject } from '@/types/image';
import { DEFAULT_TEXT_CONFIG, FONT_OPTIONS } from '@/constants/app-config';
import { createImageWithText, canvasToBlob, downloadImage, loadImage } from '@/lib/image-processing';

interface ImageEditorProps {
  imageData: ImageUpload;
  detectedObjects: DetectedObject[];
  onBack: () => void;
}

export default function ImageEditor({ imageData, detectedObjects, onBack }: ImageEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [textOverlay, setTextOverlay] = useState<TextOverlay>({
    ...DEFAULT_TEXT_CONFIG,
    // Position text behind the first detected object if available
    x: detectedObjects[0] ? Math.max(5, detectedObjects[0].x - 10) : 50,
    y: detectedObjects[0] ? detectedObjects[0].y + (detectedObjects[0].height * 0.3) : 50,
  });
  const [isDownloading, setIsDownloading] = useState(false);

  // Update canvas when text overlay changes
  useEffect(() => {
    updateCanvas();
  }, [textOverlay, imageData]);

  const updateCanvas = async () => {
    if (!canvasRef.current) return;

    try {
      const img = await loadImage(imageData.file);
      const canvas = createImageWithText(img, textOverlay, detectedObjects[0]);
      
      // Copy the result to our display canvas
      const ctx = canvasRef.current.getContext('2d');
      if (ctx) {
        canvasRef.current.width = canvas.width;
        canvasRef.current.height = canvas.height;
        ctx.drawImage(canvas, 0, 0);
      }
    } catch (error) {
      console.error('Failed to update canvas:', error);
    }
  };

  const handleDownload = async (format: 'png' | 'jpeg' = 'png') => {
    if (!canvasRef.current) return;

    setIsDownloading(true);
    try {
      const blob = await canvasToBlob(canvasRef.current, format);
      const filename = `text-behind-image.${format}`;
      downloadImage(blob, filename);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const updateTextProperty = <K extends keyof TextOverlay>(
    property: K,
    value: TextOverlay[K]
  ) => {
    setTextOverlay(prev => ({ ...prev, [property]: value }));
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Canvas Preview */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Preview</h2>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleDownload('png')}
                  disabled={isDownloading}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  <Download className="w-4 h-4" />
                  <span>PNG</span>
                </button>
                <button
                  onClick={() => handleDownload('jpeg')}
                  disabled={isDownloading}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  <Download className="w-4 h-4" />
                  <span>JPG</span>
                </button>
              </div>
            </div>
            
            <div className="border rounded-lg overflow-hidden bg-gray-50">
              <canvas
                ref={canvasRef}
                className="max-w-full h-auto"
                style={{ maxHeight: '500px' }}
              />
            </div>
          </div>
        </div>

        {/* Controls Panel */}
        <div className="space-y-6">
          {/* Text Content */}
          <div className="bg-white rounded-lg shadow-lg p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Type className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-medium text-gray-900">Text</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content
                </label>
                <input
                  type="text"
                  value={textOverlay.content}
                  onChange={(e) => updateTextProperty('content', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter text..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Font Family
                </label>
                <select
                  value={textOverlay.fontFamily}
                  onChange={(e) => updateTextProperty('fontFamily', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {FONT_OPTIONS.map(font => (
                    <option key={font} value={font}>{font}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Font Size: {textOverlay.fontSize}px
                </label>
                <input
                  type="range"
                  min="20"
                  max="120"
                  value={textOverlay.fontSize}
                  onChange={(e) => updateTextProperty('fontSize', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Color */}
          <div className="bg-white rounded-lg shadow-lg p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Palette className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-medium text-gray-900">Color</h3>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Text Color
              </label>
              <input
                type="color"
                value={textOverlay.color}
                onChange={(e) => updateTextProperty('color', e.target.value)}
                className="w-full h-10 border border-gray-300 rounded-lg cursor-pointer"
              />
            </div>
          </div>

          {/* Position */}
          <div className="bg-white rounded-lg shadow-lg p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Move className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-medium text-gray-900">Position</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Horizontal: {textOverlay.x}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={textOverlay.x}
                  onChange={(e) => updateTextProperty('x', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Vertical: {textOverlay.y}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={textOverlay.y}
                  onChange={(e) => updateTextProperty('y', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Back Button */}
          <button
            onClick={onBack}
            className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Upload New Image
          </button>
        </div>
      </div>
    </div>
  );
}
