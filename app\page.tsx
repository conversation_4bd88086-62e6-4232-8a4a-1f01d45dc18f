'use client';

import React, { useState } from 'react';
import ImageUploader from '@/components/ImageUploader';
import ImageEditor from '@/components/ImageEditor';
import LoadingSpinner from '@/components/LoadingSpinner';
import { ImageUpload, DetectedObject } from '@/types/image';
import { detectObjects, hasDetectedObjects } from '@/lib/object-detection';
import { ERROR_MESSAGES } from '@/constants/app-config';

type AppState = 'upload' | 'processing' | 'editing' | 'error';

export default function Home() {
  const [appState, setAppState] = useState<AppState>('upload');
  const [imageData, setImageData] = useState<ImageUpload | null>(null);
  const [detectedObjects, setDetectedObjects] = useState<DetectedObject[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleImageUpload = async (uploadedImage: ImageUpload) => {
    setImageData(uploadedImage);
    setAppState('processing');
    setError(null);

    try {
      // Detect objects in the uploaded image
      const objects = await detectObjects(uploadedImage.preview);

      if (!hasDetectedObjects(objects)) {
        setError(ERROR_MESSAGES.NO_OBJECT_DETECTED);
        setAppState('error');
        return;
      }

      setDetectedObjects(objects);
      setAppState('editing');
    } catch (err) {
      console.error('Object detection failed:', err);
      setError(ERROR_MESSAGES.PROCESSING_ERROR);
      setAppState('error');
    }
  };

  const handleBackToUpload = () => {
    setAppState('upload');
    setImageData(null);
    setDetectedObjects([]);
    setError(null);
  };

  const renderContent = () => {
    switch (appState) {
      case 'upload':
        return (
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Text-Behind
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              Add text behind your photos, instantly
            </p>
            <ImageUploader onImageUpload={handleImageUpload} />
          </div>
        );

      case 'processing':
        return (
          <LoadingSpinner
            message="Analyzing your image and detecting objects..."
            size="lg"
          />
        );

      case 'editing':
        return imageData ? (
          <ImageEditor
            imageData={imageData}
            detectedObjects={detectedObjects}
            onBack={handleBackToUpload}
          />
        ) : null;

      case 'error':
        return (
          <div className="text-center">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
              <h2 className="text-lg font-semibold text-red-800 mb-2">
                Processing Failed
              </h2>
              <p className="text-red-700 mb-4">{error}</p>
              <button
                onClick={handleBackToUpload}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Try Another Image
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {renderContent()}
      </div>
    </div>
  );
}
