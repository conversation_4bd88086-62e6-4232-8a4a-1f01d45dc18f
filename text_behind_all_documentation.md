# Text-Behind

## Project Description
I want to build a web app where use can come and upload there image and can add text behind them. It will be Text behind image app with ai. the user will come and upload a image and the we will detect the object(human) and some text will be already added like "POV" behind them. then user can edit the text, colour, font position etc. I want to build the app fully on Nextjs. If some api key is needed for AI, I have gemeni.

## Product Requirements Document
Product Requirements Document (PRD)

1. Introduction
Text-Behind is a web application designed to allow users to effortlessly add customizable text behind detected objects (primarily humans) in their uploaded images. The application aims to automate the process of placing text behind foreground elements, providing a modern, minimalist, and professional user experience. This document outlines the core features, functional and non-functional requirements, technical specifications, and key considerations for the development of Text-Behind.

2. Goals
*   To provide an intuitive web application for automatically placing text behind image subjects.
*   To enable users to customize the appearance of the text (content, color, font, position).
*   To deliver a fast and accurate image processing experience.
*   To allow users to download their processed images in common formats.

3. Target Audience & Use Cases
The primary target audience for Text-Behind is anyone who wishes to add text behind an object in an image without manual, complex photo editing. This includes casual users, social media enthusiasts, and content creators looking for a quick and automated solution. A core use case is placing a default phrase like "POV" behind a person in a photo, which can then be easily customized.

4. Scope

4.1. In-Scope for MVP
*   User Authentication via Clerk.
*   Image upload functionality (PNG/JPG, max 5MB).
*   Automatic object (human) detection within the uploaded image.
*   Automatic placement of default text ("POV") behind the detected object.
*   User interface for customizing text: content, color, font, and position.
*   Image download functionality (PNG/JPG).
*   Basic error handling for cases like "no object detected".
*   Modern, minimalist, and professional UI/UX design.

4.2. Out-of-Scope for MVP
*   Advanced AI features (e.g., text generation, complex background manipulation beyond simple object detection for text placement, integration with Gemini API beyond core object detection if applicable to the chosen detection method).
*   User account history or saving of past processed images.
*   High priority on mobile responsiveness (will be considered in future iterations).
*   CI/CD pipeline implementation.
*   Advanced image editing tools beyond text customization.
*   Support for image formats other than PNG/JPG.

5. Core Features & Functionality

5.1. User Authentication
*   Users will be able to sign up and log in using Clerk for full authentication management.
*   No user history or past processed images will be stored on the server side.

5.2. Image Upload
*   Users can upload an image from their local device.
*   Supported image formats: PNG and JPG.
*   Maximum image file size: 5 MB.

5.3. Image Processing & Object Detection
*   Upon image upload, the system will automatically process the image to detect foreground objects, specifically humans.
*   The object detection should be as precise as possible to accurately place text behind the detected subject.
*   If no human or primary object is detected, a toast message will be displayed to the user (e.g., "No object detected. Please upload another image."). The text placement feature will not proceed in this scenario.

5.4. Default Text Placement
*   After successful object detection, the default text "POV" will be automatically placed behind the detected object (human).
*   The initial placement will be optimized for visibility and common use cases.

5.5. Text Customization
*   Users will be able to edit the content of the text (e.g., change "POV" to any custom string).
*   Users will be able to select the color of the text.
*   Users will be able to choose from a selection of fonts.
*   Users will be able to adjust the position of the text relative to the detected object and image boundaries.

5.6. Image Download
*   Users can download the final processed image.
*   Downloadable formats: PNG and JPG.
*   The downloaded image should maintain reasonable quality and resolution.

6. User Experience (UX) & Design
*   The application's design will be modern, minimalist, and professional.
*   The user interface should be intuitive and easy to navigate.
*   Feedback mechanisms (e.g., loading indicators, success/error toasts) will be implemented to guide the user through the process.
*   Mobile responsiveness is not a high priority for the initial MVP but will be considered for future enhancements.

7. Technical Requirements & Architecture

7.1. Frontend
*   Technology: Next.js

7.2. Backend
*   Technology: Next.js (for API routes/server-side logic).
*   Database: MongoDB (Specific use case for MVP not extensively defined beyond general data storage; potentially for future features or application-level configurations not handled by Clerk).
*   Authentication: Clerk (handles user registration, login, and session management).

7.3. Deployment
*   Platform: Vercel.
*   CI/CD: Not required for MVP.

7.4. AI/Processing Layer
*   Object Detection: An external library or service capable of precise human detection will be integrated. (Note: While advanced AI features like Gemini are out of scope for general image enhancement, the core object detection required for "Text-Behind" falls under "AI capabilities" and is fundamental to the app's functionality).

8. Non-Functional Requirements

8.1. Performance
*   The application should feel fast and responsive throughout the user flow.
*   Image upload and processing times should be minimized to provide a smooth user experience.
*   Object detection and text rendering must be accurate.

8.2. Scalability
*   The architecture should be designed with future scalability in mind, leveraging Next.js and Vercel's capabilities, even if not fully optimized for high load in MVP.

8.3. Reliability
*   The application should be stable and robust, minimizing crashes or unexpected errors.
*   Error messages should be user-friendly and actionable.

8.4. Security
*   User authentication will be handled securely by Clerk.
*   Image uploads will be handled securely.
*   Standard web security practices (e.g., input validation, secure headers) will be followed.

9. Timeline & Budget Considerations
*   Timeline: The goal is to deliver a fully functional application within 4 weeks.
*   Budget: (Not specified in questionnaire, assuming resources are available for the 4-week timeline and chosen tech stack).

10. Future Features (Out of Scope for MVP)
*   Integration of advanced AI features (e.g., generative AI for text suggestions, more complex image manipulations).
*   Saving user history of processed images.
*   Full mobile responsiveness.
*   Additional text effects (e.g., shadows, outlines).
*   Support for multiple objects/humans in a single image with individual text placement.
*   Social media sharing options.
*   Custom presets for text styles.

## Technology Stack
TECHSTACK

This document outlines the proposed technology stack for the "Text-Behind" web application, covering frontend, backend, database, AI/image processing, authentication, deployment, and development tools. The selections prioritize rapid development, performance, scalability, and alignment with the project's core requirements and specified constraints.

**1. Core Technologies**

*   **Frontend & Backend Framework: Next.js**
    *   **Justification:** User's explicit choice. Next.js is a React framework that offers powerful features for building full-stack web applications. Its capabilities include Server-Side Rendering (SSR) for initial page load performance, Static Site Generation (SSG) for potential future static content, and integrated API routes that allow the backend logic (like image processing) to reside within the same codebase. This unification streamlines development, improves developer experience, and leverages Vercel's optimized deployment for Next.js applications.
*   **Database: MongoDB**
    *   **Justification:** User's explicit choice. MongoDB is a NoSQL document database, offering flexibility and scalability for handling data. While the initial scope does not require saving user history or complex data relationships, MongoDB provides a versatile foundation for potential future features like user accounts, preferences, or storing metadata related to generated images. Its schema-less nature allows for rapid iteration during development.

**2. Image Processing & AI Capabilities**

*   **Human Object Detection: TensorFlow.js (with pre-trained models like BodyPix/PoseNet)**
    *   **Justification:** The core functionality requires "Detecting the Object (human)" to place text behind it, even with the directive to "Skip the AI feature as of now" (interpreted as skipping custom AI model development). TensorFlow.js enables running machine learning models directly in the browser or on the server (Node.js environment via Next.js API routes). Utilizing pre-trained models like BodyPix or PoseNet allows for robust human segmentation or pose estimation, providing the necessary data (masks or coordinates) to precisely identify the human figure. Performing this operation on the server-side via Next.js API routes ensures consistent performance, leverages server computing power, and protects any potential API keys or sensitive logic, while a client-side preview might be considered for instant feedback.
*   **Image Manipulation: Sharp (Node.js Library)**
    *   **Justification:** Critical for the "text behind" effect and image output. Sharp is a high-performance Node.js library for converting, resizing, and manipulating images. It will be used on the Next.js backend (API routes) to:
        *   Process uploaded images.
        *   Apply the human object mask generated by TensorFlow.js (or similar output).
        *   Render and composite the custom text layer (with customizable font, color, position) precisely behind the detected human object.
        *   Output the final image in desired formats (PNG/JPG).
    *   **Client-Side Image Preview/Customization: HTML5 Canvas API**
        *   **Justification:** For real-time user customization of text (position, color, font) and immediate visual feedback without constant server roundtrips. The Canvas API provides a powerful and performant way to render and manipulate images and text directly in the browser, allowing users to fine-tune their design before final server-side processing and download.

**3. Authentication**

*   **Authentication Service: Clerk**
    *   **Justification:** User's explicit choice. Clerk is a comprehensive authentication and user management platform that integrates seamlessly with Next.js. It provides pre-built UI components and backend services for robust user authentication (signup, sign-in, password management), profile management, and session handling. This significantly reduces development time and complexity associated with building a secure authentication system from scratch, allowing the team to focus on core application features.

**4. User Interface & Styling**

*   **CSS Framework: Tailwind CSS**
    *   **Justification:** Aligns with the "modern, minimalist, and professional" design vision. Tailwind CSS is a utility-first CSS framework that enables rapid UI development by composing low-level utility classes directly in the markup. Its highly customizable nature allows for precise control over the design, ensuring consistency and a clean aesthetic. It integrates excellently with Next.js.
*   **UI Component Library: Shadcn UI**
    *   **Justification:** Built on Tailwind CSS and Radix UI, Shadcn UI provides beautifully designed, unstyled components that are easily customizable. It offers a set of high-quality, accessible UI primitives that can be quickly assembled to form complex interfaces while maintaining the desired minimalist and professional look. This accelerates UI development without sacrificing design flexibility.

**5. Deployment**

*   **Platform: Vercel**
    *   **Justification:** User's explicit choice and the recommended deployment platform for Next.js applications. Vercel offers zero-configuration deployment for Next.js, automatic scaling, a global CDN for fast content delivery, and integrated CI/CD (though initial CI/CD setup is out of scope, Vercel lays the groundwork for future automation). Its seamless integration with GitHub streamlines the deployment process for every code push.

**6. Development Tools & Languages**

*   **Programming Language: TypeScript**
    *   **Justification:** Enhances code quality, maintainability, and developer experience by adding static type checking to JavaScript. It catches errors early in development, improves code readability, and facilitates refactoring, especially crucial for a fast-paced development cycle and future team collaboration. TypeScript is a standard in the modern Next.js ecosystem.
*   **Package Manager: pnpm**
    *   **Justification:** While npm or Yarn are viable, pnpm is often chosen for its efficiency in managing node_modules (symlinks instead of copying) leading to faster installation times and significant disk space savings, which can be beneficial in a rapidly developing project.
*   **Code Quality & Formatting: ESLint & Prettier**
    *   **Justification:** ESLint helps identify and report on problematic patterns found in JavaScript/TypeScript code, improving code quality. Prettier is an opinionated code formatter that enforces a consistent style across the codebase, reducing stylistic debates and improving readability for all contributors. These tools are essential for maintaining a clean, consistent, and error-free codebase, especially within a compressed development timeline.

## Project Structure
PROJECTSTRUCTURE

This document outlines the proposed file and folder structure for the "Text-Behind" web application. The structure is designed to promote modularity, maintainability, and scalability, aligning with Next.js App Router conventions and best practices for modern web development.

### 1. Root Directory (`/`)

This level contains global configuration files, documentation, and the primary application source code directory.

*   `app/`
*   `public/`
*   `components/`
*   `lib/`
*   `utils/`
*   `styles/`
*   `types/`
*   `hooks/`
*   `db/`
*   `constants/`
*   `services/`
*   `middleware.ts`
*   `.env.local`
*   `next.config.js`
*   `tailwind.config.ts`
*   `postcss.config.js`
*   `tsconfig.json`
*   `package.json`
*   `README.md`
*   `.gitignore`

### 2. `app/`

This directory is the heart of the Next.js App Router, containing all page routes, layouts, and API routes. It defines the core application structure and UI.

*   `layout.tsx`
    *   **Description:** Defines the root layout for the application, including the `<html>` and `<body>` tags, global styles, and context providers (e.g., ClerkProvider).
*   `page.tsx`
    *   **Description:** The main landing page of the application (`/`), responsible for rendering the image upload and editing interface.
*   `globals.css`
    *   **Description:** Global CSS styles, typically imported into `layout.tsx` to apply styles across the entire application.
*   `api/`
    *   **Description:** Contains serverless API routes that run on the Next.js server.
    *   `process-image/`
        *   `route.ts`
            *   **Description:** Handles the image processing logic. This endpoint receives the uploaded image, performs object detection (or a placeholder as AI is skipped for now), places the default "POV" text behind the detected object, and applies any user customizations (text content, color, font, position). It then returns the processed image for download.
    *   `clerk-webhook/`
        *   `route.ts`
            *   **Description:** (Optional) An endpoint to listen for webhooks from Clerk, useful for synchronizing user data with MongoDB or triggering specific actions upon user authentication events.
*   `_components/`
    *   **Description:** Components specific to the `app` directory's layouts or pages, often including core UI elements like headers or footers.
    *   `Header.tsx`
        *   **Description:** The application's header component, typically containing the logo and authentication controls.
    *   `Footer.tsx`
        *   **Description:** The application's footer component.
    *   `AuthWrapper.tsx`
        *   **Description:** A component that wraps the main application content with Clerk's `ClerkProvider` to provide authentication context to all child components.

### 3. `public/`

This directory serves static assets directly. Files placed here are accessible from the root of the application.

*   `images/`
    *   **Description:** Stores static image assets such as the application logo, default background images, or icons.
    *   `logo.svg`
*   `fonts/`
    *   **Description:** Contains custom font files used within the application.

### 4. `components/`

This directory houses all reusable React components, promoting modularity and code reusability across the application.

*   `ui/`
    *   **Description:** Generic, atomic UI elements that are reusable across different parts of the application without specific business logic.
    *   `Button.tsx`
        *   **Description:** A customizable button component.
    *   `Input.tsx`
        *   **Description:** A customizable input field component.
    *   `Modal.tsx`
        *   **Description:** A generic modal dialog component.
    *   `Slider.tsx`
        *   **Description:** A slider component for adjusting values (e.g., text position).
    *   `Spinner.tsx`
        *   **Description:** A loading spinner component.
    *   `ColorPicker.tsx`
        *   **Description:** A component for selecting text color.
    *   `FontSelector.tsx`
        *   **Description:** A component for selecting text font.
*   `auth/`
    *   **Description:** Components specifically for user authentication, interacting with Clerk.
    *   `SignInButton.tsx`
        *   **Description:** A button that triggers Clerk's sign-in flow.
    *   `UserProfileMenu.tsx`
        *   **Description:** A component displaying user profile information and options, likely integrated with Clerk's `UserButton`.
*   `ImageUploadForm.tsx`
    *   **Description:** The component responsible for allowing users to upload their images.
*   `ImageEditor.tsx`
    *   **Description:** The central component for displaying the uploaded image and providing controls for customizing the text (content, color, font, position).
*   `DownloadOptions.tsx`
    *   **Description:** A component presenting options for downloading the processed image (e.g., PNG, JPG).
*   `ToastMessage.tsx`
    *   **Description:** A component for displaying non-intrusive, temporary toast notifications (e.g., "No object detected").

### 5. `lib/`

This directory contains backend-specific utilities, server-side logic, and integration points, often used in API routes or server components.

*   `image-processing/`
    *   **Description:** Encapsulates the core image manipulation and processing logic.
    *   `object-detection.ts`
        *   **Description:** Placeholder for AI-powered object detection logic. Currently, it would contain a mocked or simplified version until Gemini AI integration.
    *   `text-overlay.ts`
        *   **Description:** Functions for precisely rendering and positioning text behind detected objects on an image.
    *   `image-conversion.ts`
        *   **Description:** Utilities for converting image buffers to desired output formats (PNG/JPG).
    *   `utils.ts`
        *   **Description:** Internal utility functions specifically for image processing.
*   `auth.ts`
    *   **Description:** Server-side utilities related to authentication, used to protect API routes or verify user sessions (e.g., functions wrapping Clerk's API).
*   `db.ts`
    *   **Description:** Contains the MongoDB connection setup and configuration.

### 6. `utils/`

This directory houses frontend-specific helper functions and client-side utilities that are generally applicable across the client-side of the application.

*   `helpers.ts`
    *   **Description:** General-purpose utility functions used across the client-side (e.g., file name formatting, basic data transformations).
*   `validation.ts`
    *   **Description:** Functions for client-side input validation (e.g., image file type, size).
*   `canvas.ts`
    *   **Description:** Utilities for client-side HTML Canvas API manipulation if any part of the image editing or preview is handled directly on the client.

### 7. `styles/`

This directory manages global styles and Tailwind CSS configuration.

*   `tailwind.css`
    *   **Description:** The main file for importing Tailwind CSS directives and custom CSS rules.
*   `_variables.css`
    *   **Description:** (Optional) Defines custom CSS variables for design consistency.

### 8. `types/`

Dedicated to TypeScript type definitions and interfaces, ensuring strong typing and improving code clarity and error detection.

*   `image.d.ts`
    *   **Description:** Interfaces for image objects, upload parameters, and processing results.
*   `text.d.ts`
    *   **Description:** Interfaces for customizable text properties (content, color, font, position).
*   `user.d.ts`
    *   **Description:** Interfaces for user data and authentication-related types.
*   `global.d.ts`
    *   **Description:** Global type declarations that might be used across multiple modules.

### 9. `hooks/`

Contains custom React hooks to encapsulate reusable stateful logic and side effects, promoting clean and maintainable component logic.

*   `useImageUpload.ts`
    *   **Description:** A custom hook for managing the state and logic related to image file selection and initial upload.
*   `useImageEditor.ts`
    *   **Description:** A custom hook for managing the state and interactions within the image editor (e.g., text position, font selection, color changes).
*   `useDownload.ts`
    *   **Description:** A custom hook for handling the image download process, including format selection (PNG/JPG).

### 10. `db/`

This directory structures the database interaction logic.

*   `connect.ts`
    *   **Description:** Contains the logic to establish and manage the MongoDB connection using Mongoose.
*   `models/`
    *   **Description:** Defines Mongoose schemas and models for data stored in MongoDB.
    *   `User.ts`
        *   **Description:** The Mongoose model for user data, primarily to store additional user-specific settings or preferences not handled by Clerk directly (as user history is not saved, this might store general app settings).

### 11. `constants/`

Stores immutable, application-wide constants for easy access and modification.

*   `app-config.ts`
    *   **Description:** General application configuration values, such as image size limits (e.g., 5 MB).
*   `text-defaults.ts`
    *   **Description:** Default values for the initial "POV" text content, color, font, and position.
*   `error-messages.ts`
    *   **Description:** Centralized error messages for consistent user feedback (e.g., "No object detected", "Image too large").

### 12. `services/`

(Optional but Recommended) This layer abstracts complex business logic or orchestrates multiple utility functions, providing a higher-level interface for core features.

*   `imageService.ts`
    *   **Description:** A service that orchestrates the end-to-end image workflow, integrating image upload, processing via `lib/image-processing`, and preparing for download.

### 13. `middleware.ts`

*   **Description:** Next.js middleware file for implementing authentication checks and other request processing logic (e.g., using Clerk's `authMiddleware` to protect routes).

### 14. Environment Files (`.env.local`)

*   **Description:** Stores sensitive information and configuration variables that vary between environments (e.g., `MONGODB_URI`, `CLERK_SECRET_KEY`, `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`). This file is not committed to version control.

### 15. Configuration Files

*   `next.config.js`
    *   **Description:** Next.js configuration file for customizing build behavior, image optimization, and other settings.
*   `tailwind.config.ts`
    *   **Description:** Tailwind CSS configuration file for defining themes, colors, fonts, and plugins.
*   `postcss.config.js`
    *   **Description:** PostCSS configuration for processing CSS.
*   `tsconfig.json`
    *   **Description:** TypeScript configuration file defining compilation options.

### 16. Project Metadata & Version Control

*   `package.json`
    *   **Description:** Defines project metadata, scripts for development and building, and project dependencies.
*   `README.md`
    *   **Description:** Provides a general overview of the project, setup instructions, and key features.
*   `.gitignore`
    *   **Description:** Specifies files and directories that Git should ignore (e.g., `node_modules`, `.env.local`, build artifacts).

This comprehensive structure ensures that the "Text-Behind" application is well-organized, scalable for future AI integration, and easy to navigate for development and maintenance.

## Database Schema Design
SCHEMADESIGN

This section outlines the database schema design for the "Text-Behind" application. Given the current requirements, particularly "we don't need to save history for the user as of now", the primary focus is on application-level configurations rather than persistent user-specific data or generated content. The database chosen is MongoDB, a NoSQL document database.

DATABASE STRUCTURE

1.  
**Collections**
    *   **AppSettings**: Stores global application configurations and default values.

2.  
**Collection Details**

    **Collection Name: AppSettings**

    *   **Purpose**: This collection is designed to store application-wide default settings and configurable parameters that are not tied to individual user sessions or specific generated images. This allows for dynamic modification of defaults (e.g., initial text, font, color) without requiring code deployments.

    *   **Data Model (Document Structure)**:
        *   `_id`: ObjectId
            *   **Description**: The unique identifier for the document. In a single-document configuration pattern, this could be a fixed string (e.g., "global_settings") for easy retrieval, or a standard MongoDB ObjectId.
            *   **Type**: ObjectId | String
            *   **Required**: Yes

        *   `defaultText`: String
            *   **Description**: The default text string that will be automatically placed behind the detected object (e.g., "POV").
            *   **Type**: String
            *   **Required**: Yes
            *   **Example**: "POV"

        *   `defaultFontFamily`: String
            *   **Description**: The default font family to be applied to the text (e.g., "Arial", "Roboto", "Impact").
            *   **Type**: String
            *   **Required**: Yes
            *   **Example**: "Arial"

        *   `defaultFontSize`: Number
            *   **Description**: The default size of the text in a suitable unit (e.g., pixels).
            *   **Type**: Number
            *   **Required**: Yes
            *   **Example**: 60

        *   `defaultTextColor`: String
            *   **Description**: The default color of the text, typically in hexadecimal format or a recognized color name.
            *   **Type**: String
            *   **Required**: Yes
            *   **Example**: "#FFFFFF" (white)

        *   `defaultTextPositionPreset`: String
            *   **Description**: A predefined string indicating the default positioning logic or preset for the text relative to the detected object or image. This can be refined to a more complex object if detailed coordinates are needed later.
            *   **Type**: String
            *   **Required**: Yes
            *   **Example**: "behind-object-center"

        *   `lastModified`: Date
            *   **Description**: Timestamp of when these settings were last updated.
            *   **Type**: Date
            *   **Required**: No (system-managed)

        *   `modifiedBy`: String
            *   **Description**: Identifier for who last modified these settings (e.g., "system" or an admin user's ID, if an admin panel is implemented).
            *   **Type**: String
            *   **Required**: No
            *   **Example**: "system"

    *   **Relationships**: The `AppSettings` collection operates independently and does not have direct relationships with other collections, as no user-specific content or history is currently persisted.

    *   **Indexes**: No specific indexes are critically required for this collection beyond the default `_id` index, as it is expected to contain only one or a very small number of documents, leading to efficient full-collection scans for settings retrieval.

3.  
**Rationale for Omitted Collections**

    *   **User Data / User Accounts**: User authentication is handled externally by Clerk. There is no need for a dedicated `Users` collection within this database, as user profiles and authentication states are managed by Clerk.

    *   **Image Data / Processed Content History**: The project explicitly states, "we don't need to save history for the user as of now." This means that uploaded images, the results of AI processing (object detection), and the customized text configurations are transient. They are processed in real-time and offered for download. No persistent storage is required for these items, removing the need for `Images` or `UserCreations` collections. Should history or user-specific saves become a future requirement, these collections would be added at that time, likely linking to a Clerk user ID.

## User Flow
USERFLOW

1. Introduction
This document outlines the primary user journeys and interaction patterns for the "Text-Behind" web application. The core flow focuses on enabling users to upload an image, automatically detect an object (human), place default text behind it, customize this text, and then download the modified image. The user experience aims to be modern, minimalist, and professional, prioritizing speed and accuracy.

2. User Journey: Image Upload & Text Generation

2.1. Accessing the Application
    *   **User Action:** The user navigates to the Text-Behind web application URL (e.g., www.textbehind.com).
    *   **System Response:** The application loads and displays the main landing page.
    *   **Wireframe Description:**
        *   A clean, uncluttered layout with a central "Upload Image" button or a large, prominent drag-and-drop zone.
        *   A concise tagline, such as "Add text behind your photos, instantly."
        *   No immediate requirement for user login; the core functionality is accessible without authentication initially.
    *   **Interaction Patterns:**
        *   Clicking the "Upload Image" button.
        *   Dragging an image file directly onto the designated drop zone.

2.2. Image Upload
    *   **User Action:** The user selects an image file (JPG or PNG) from their local machine or drags it onto the drop zone.
    *   **System Response:**
        *   **Validation:** The system immediately validates the uploaded file's type and size (max 5MB).
            *   If valid: Displays a progress indicator (e.g., spinner, progress bar) indicating upload in progress.
            *   If invalid (wrong type or too large): Displays a transient "toast" notification (e.g., \"Unsupported file type. Please upload a JPG or PNG image.\" or \"File too large. Maximum size is 5MB.\") and remains in the upload state.
        *   **Upload:** Uploads the image to the server.
    *   **Wireframe Description:**
        *   An interactive upload area that visually transforms to show progress upon file selection/drop.
        *   A clear visual representation of file type/size constraints.
        *   Temporary overlay or toast message area for validation feedback.
    *   **Interaction Patterns:**
        *   Clicking to open file explorer dialog.
        *   Dragging and dropping the image file.

2.3. Image Processing & Initial Text Placement
    *   **User Action:** The user waits for the system to process the uploaded image.
    *   **System Response:**
        *   **Processing Indicator:** Displays a full-screen or prominent loading indicator (e.g., "Processing Image...", "Detecting Object...") while the server-side AI processes the image.
        *   **Object Detection:** The AI module attempts to detect an object (specifically a human) within the image.
        *   **Initial Text Placement:**
            *   If a human object is successfully detected: The system automatically renders the image on the canvas with the default text "POV" placed precisely behind the detected object.
            *   If no human object is detected: A toast notification is displayed: \"No object detected. Please try another image.\". The system then returns the user to the initial image upload state.
        *   **Transition to Editor:** Upon successful processing and text placement, the interface transitions to the customization/editing view.
    *   **Wireframe Description:**
        *   A central, large canvas area displaying the uploaded image.
        *   The "POV" text is visually positioned behind the main detected human figure.
        *   A dedicated panel (e.g., sidebar or bottom bar) for editing controls.
    *   **Interaction Patterns:** Automatic system processing.

2.4. Text Customization
    *   **User Action:** The user interacts with the editing controls to modify the text, its appearance, and its position.
    *   **System Response:** All changes made through the controls are applied and reflected on the main image canvas in real-time.
    *   **Wireframe Description:**
        *   **Main Canvas:** The primary area showing the image and the editable text.
        *   **Editing Panel Controls:**
            *   **Text Input:** A text field allowing the user to change the "POV" text to any custom string.
            *   **Color Picker:** A graphical color selection tool (e.g., palette, eyedropper, hex input) to change the text color.
            *   **Font Selector:** A dropdown menu offering a selection of available font families.
            *   **Position Controls:** Sliders or directional buttons (e.g., X, Y coordinates) to finely adjust the text's horizontal and vertical placement relative to the detected object.
    *   **Interaction Patterns:**
        *   Typing directly into the text input field.
        *   Clicking and selecting colors from the color picker.
        *   Selecting a font from the dropdown list.
        *   Dragging sliders or clicking buttons to adjust position.

2.5. Download Edited Image
    *   **User Action:** Once satisfied with the customizations, the user clicks the "Download" button.
    *   **System Response:**
        *   **Image Generation:** The system renders the final image, incorporating all user customizations (text content, color, font, position) on top of the original image, with the text behind the object.
        *   **Download Initiation:** Triggers a browser-level download of the generated image file.
        *   **Format Options:** Offers the choice to download as a PNG or JPG file.
    *   **Wireframe Description:**
        *   A prominent "Download" button, possibly accompanied by radio buttons or a dropdown for selecting the output format (PNG/JPG).
        *   A brief "Generating image..." indicator before the download begins.
    *   **Interaction Patterns:**
        *   Clicking the "Download" button.
        *   Selecting the desired file format (PNG or JPG).

3. Authentication Flow (Clerk)

3.1. User Sign-In/Sign-Up (Optional)
    *   **User Action:** If authentication features are introduced (e.g., for saved history, premium features - not in scope for V1), the user would click a "Sign In" or "Sign Up" link.
    *   **System Response:** The application redirects the user to the Clerk-hosted authentication UI for sign-in or registration.
    *   **Wireframe Description:** "Sign In" / "Sign Up" links typically in the header or footer, appearing only if authentication is a required step for specific functionality.
    *   **Interaction Patterns:** Clicking authentication links.

4. Error Handling & Edge Cases

4.1. Invalid File Upload
    *   **Scenario:** User attempts to upload a file that is not a JPG/PNG or exceeds 5MB.
    *   **System Response:** Displays a toast notification: \"Unsupported file type. Please upload a JPG or PNG image.\" or \"File too large. Maximum size is 5MB.\"
    *   **Recovery:** The user must select a valid file to proceed.

4.2. No Object Detected
    *   **Scenario:** The uploaded image does not contain a discernible human or primary object for text placement.
    *   **System Response:** Displays a toast notification: \"No object detected. Please try another image.\" The user is then returned to the image upload screen.
    *   **Recovery:** The user must upload a different image with a clear object.

4.3. Processing Error
    *   **Scenario:** An unexpected error occurs during image processing (e.g., AI service unavailable, internal server error).
    *   **System Response:** Displays a generic error toast: \"An error occurred during processing. Please try again later.\"
    *   **Recovery:** The user can retry the process. If persistent, they may need to report the issue.

## Styling Guidelines
STYLING

1. DESIGN SYSTEM PRINCIPLES

The "Text-Behind" application's design ethos is rooted in a modern, minimalist, and professional aesthetic. The primary goal is to provide a seamless, intuitive, and efficient user experience, making complex image manipulation feel simple and accessible.

    *   **Clarity & Simplicity**: The interface must be uncluttered, focusing on core functionality. Every element should serve a clear purpose, reducing cognitive load for the user.
    *   **Consistency**: A unified visual language will be maintained across all components, from buttons and inputs to typography and spacing. This ensures predictability and ease of use.
    *   **Efficiency**: The user flow is streamlined to minimize steps and clicks. Direct manipulation and immediate feedback are prioritized to make the editing process feel fast and responsive.
    *   **Professionalism**: The design will reflect a high standard of quality, instilling trust and confidence in the application's capabilities, particularly given its AI-driven features.
    *   **Feedback**: Users must receive clear and immediate feedback for their actions (e.g., loading states, success/error messages like "No object detected" toasts).

2. COLOR PALETTE

The color palette is intentionally restrained to support the minimalist and professional vision, allowing the user's image and text customization to be the focal point. A light theme will be the default and primary focus.

    *   **Primary Accent**: `#4A90E2` (A professional, calming blue, used for primary calls to action, interactive elements, and key brand accents.)
    *   **Secondary Accent**: `#50E3C2` (A subtle, fresh teal for secondary interactive elements or highlights, providing a harmonious contrast.)
    *   **Text - Primary**: `#333333` (Dark charcoal for main body text, headings, and high-contrast elements.)
    *   **Text - Secondary**: `#666666` (Medium grey for secondary information, labels, and less prominent text.)
    *   **Background - Canvas**: `#FFFFFF` (Pure white for main content areas and backgrounds, providing a clean slate.)
    *   **Background - Subtle**: `#F8F8F8` (Very light grey for subtle sectioning or card backgrounds, adding depth without distraction.)
    *   **Border/Divider**: `#E0E0E0` (Light grey for subtle borders, separators, and input fields.)
    *   **Success**: `#4CAF50` (Standard green for success messages and positive indicators.)
    *   **Error**: `#FF4D4F` (Standard red for error messages and critical alerts.)
    *   **Warning**: `#FFA726` (Standard orange for warning messages.)
    *   **Disabled**: `#BBBBBB` (Light grey for disabled interactive elements.)

3. TYPOGRAPHY

Typography choices prioritize readability, modernity, and a clean aesthetic. A sans-serif font family will be used consistently throughout the application.

    *   **Font Family**: `Inter`, with `sans-serif` as a fallback. (Inter is chosen for its excellent legibility, multiple weights, and clean, modern appearance suitable for UI.)

    *   **Font Weights**: Regular (400), Medium (500), Semi-Bold (600), Bold (700).

    *   **Font Sizes (Base: 16px)**:
        *   **H1 (Page Title)**: `3rem` (`48px`) - Bold
        *   **H2 (Section Header)**: `2rem` (`32px`) - Semi-Bold
        *   **H3 (Component Title)**: `1.5rem` (`24px`) - Medium
        *   **H4 (Sub-heading)**: `1.25rem` (`20px`) - Medium
        *   **Body Text (Default)**: `1rem` (`16px`) - Regular
        *   **Small Text/Caption**: `0.875rem` (`14px`) - Regular
        *   **Button Text**: `1rem` (`16px`) - Medium

    *   **Line Height**: Default `1.5` for body text to ensure comfortable reading.
    *   **Letter Spacing**: Default `normal`, with subtle adjustments for headings if needed for visual balance (e.g., `-0.02em` for H1/H2).

4. UI/UX PRINCIPLES

These principles guide the interaction design and user experience, ensuring the app is intuitive, efficient, and enjoyable to use.

    *   **Intuitive User Flow**: The primary user journey (Upload Image -> Customize Text -> Download) will be highly visible and linear. Each step should be clearly delineated and easy to navigate.

    *   **Direct Manipulation**: For text customization (position, color, font), users should feel they are directly manipulating the elements on the canvas. Sliders, color pickers, and font selectors should provide immediate visual feedback on the image preview.

    *   **Clear Call-to-Actions (CTAs)**: Primary actions (e.g., "Upload Image", "Download") will be prominently displayed and clearly labeled using the primary accent color.

    *   **Visual Hierarchy**: Information and interactive elements will be organized to guide the user's eye naturally. Important elements will stand out, while secondary information will be less prominent but still accessible.

    *   **Feedback & States**: 
        *   **Loading States**: Visual indicators (spinners, progress bars) will communicate when the application is processing, especially during image upload and AI object detection.
        *   **Success/Error Toasts**: Brief, non-intrusive toast notifications will inform the user about the outcome of actions (e.g., "Image uploaded successfully", "No object detected", "Download complete").
        *   **Hover/Focus States**: Interactive elements (buttons, inputs) will have clear hover and focus states to indicate interactivity and current selection.

    *   **Accessibility (Baseline)**: While not a primary focus for comprehensive accessibility auditing, fundamental principles will be applied:
        *   Sufficient color contrast for readability.
        *   Clear focus indicators for keyboard navigation.
        *   Descriptive labels for interactive elements where possible.

    *   **Error Handling**: User-friendly error messages will guide users on how to resolve issues. For example, if an image is too large, a specific message will advise on the maximum file size.

    *   **Preview Area**: A large, clear preview area will be central to the interface, allowing users to see real-time changes as they customize the text.
