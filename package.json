{"name": "newproject", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@imgly/background-removal": "^1.7.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-toast": "^1.2.14", "canvas": "^3.1.2", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.34.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}